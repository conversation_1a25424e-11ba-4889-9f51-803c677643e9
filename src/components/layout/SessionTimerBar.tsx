/**
 * Session Timer Bar Component
 * 
 * Enhanced timer bar that supports session-based timing with multiple
 * timer instances, session selection, and improved visual hierarchy.
 */

import React, { useState } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  Autocomplete,
  TextField,
  Chip,
  Stack,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Pause as PauseIcon,
  Notes as NotesIcon,
  NoteAdd as NoteAddIcon,
  Add as AddIcon,
  MoreVert as MoreIcon,
  FolderOpen as SessionIcon,
} from '@mui/icons-material';
import { TaskSession, TimerInstance } from '../../types/timer';
import { Task } from '../../types/task';
import { useTimer } from '../../hooks/useTimer';
import { formatTime } from '../../utils/formatters';

interface SessionTimerBarProps {
  activeSession: TaskSession | null;
  predefinedTasks: Task[];
  onStartSession: (taskId: string, taskName: string) => Promise<TaskSession>;
  onStopSession: (sessionId: string) => Promise<void>;
  onCreateTimerInstance: (sessionId: string) => Promise<TimerInstance>;
  onStartTimer: (instanceId: string) => Promise<void>;
  onStopTimer: (instanceId: string) => Promise<void>;
  onPauseTimer: (instanceId: string) => Promise<void>;
  onResumeTimer: (instanceId: string) => Promise<void>;
  onOpenSessionNotes?: (sessionId: string) => void;
  onOpenTimerNotes?: (instanceId: string) => void;
}

export function SessionTimerBar({
  activeSession,
  predefinedTasks,
  onStartSession,
  onStopSession,
  onCreateTimerInstance,
  onStartTimer,
  onStopTimer,
  onPauseTimer,
  onResumeTimer,
  onOpenSessionNotes,
  onOpenTimerNotes,
}: SessionTimerBarProps) {
  const [selectedTask, setSelectedTask] = useState<string>('');
  const [inputValue, setInputValue] = useState<string>('');
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

  // Get running timer instance
  const runningInstance = activeSession?.timerInstances?.find(i => i.isRunning);
  
  // Calculate elapsed time for running timer
  const elapsed = useTimer(
    runningInstance?.isRunning || false,
    runningInstance?.startTime
  );

  const taskOptions = predefinedTasks.map(task => ({ label: task.name, id: task.id }));

  const handleStartSession = async () => {
    const selectedTaskObj = predefinedTasks.find(t => t.name === selectedTask);
    const taskName = selectedTask.trim() || inputValue.trim();
    const taskId = selectedTaskObj?.id || '';
    
    if (taskName) {
      try {
        const newSession = await onStartSession(taskId, taskName);
        // Automatically create and start first timer instance
        const newInstance = await onCreateTimerInstance(newSession.id);
        await onStartTimer(newInstance.id);
        setSelectedTask('');
        setInputValue('');
      } catch (error) {
        console.error('Failed to start session:', error);
      }
    }
  };

  const handleStopSession = async () => {
    if (activeSession) {
      // Stop any running timers first
      if (runningInstance) {
        await onStopTimer(runningInstance.id);
      }
      await onStopSession(activeSession.id);
    }
  };

  const handleAddTimerInstance = async () => {
    if (activeSession) {
      try {
        const newInstance = await onCreateTimerInstance(activeSession.id);
        await onStartTimer(newInstance.id);
      } catch (error) {
        console.error('Failed to add timer instance:', error);
      }
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchor(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
  };

  // No active session - show session creation interface
  if (!activeSession) {
    return (
      <Paper 
        elevation={2} 
        sx={{ 
          p: 2, 
          bgcolor: 'background.paper',
          borderRadius: 2,
          border: '1px solid',
          borderColor: 'divider'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <SessionIcon color="primary" />
          <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
            Start New Session
          </Typography>
          
          <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', gap: 2 }}>
            <Autocomplete
              value={selectedTask}
              onChange={(_, newValue) => setSelectedTask(typeof newValue === 'string' ? newValue : newValue?.label || '')}
              inputValue={inputValue}
              onInputChange={(_, newInputValue) => setInputValue(newInputValue)}
              options={taskOptions}
              freeSolo
              sx={{ minWidth: 300, flex: 1 }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  placeholder="Select or enter task name..."
                  variant="outlined"
                  size="small"
                />
              )}
            />
            
            <Button
              variant="contained"
              color="primary"
              startIcon={<PlayIcon />}
              onClick={handleStartSession}
              disabled={!selectedTask.trim() && !inputValue.trim()}
              sx={{ minWidth: 120 }}
            >
              Start Session
            </Button>
          </Box>
        </Box>
      </Paper>
    );
  }

  // Active session - show session controls and timer instances
  return (
    <Paper 
      elevation={2} 
      sx={{ 
        p: 2, 
        bgcolor: 'secondary.main',
        color: 'secondary.contrastText',
        borderRadius: 2,
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
        <SessionIcon />
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          {activeSession.taskName}
        </Typography>
        
        <Chip
          label={`${activeSession.timerInstances?.length || 0} timer${(activeSession.timerInstances?.length || 0) !== 1 ? 's' : ''}`}
          size="small"
          sx={{
            bgcolor: 'secondary.dark',
            color: 'secondary.contrastText',
            fontWeight: 600
          }}
        />
        
        <Box sx={{ flex: 1 }} />
        
        {/* Session total time */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" sx={{ opacity: 0.8 }}>
            Total:
          </Typography>
          <Typography variant="h6" sx={{ fontFamily: 'monospace', fontWeight: 600 }}>
            {formatTime(activeSession.totalDuration + (runningInstance ? elapsed : 0))}
          </Typography>
        </Box>
        
        <IconButton
          onClick={handleMenuOpen}
          sx={{ color: 'secondary.contrastText' }}
        >
          <MoreIcon />
        </IconButton>
        
        <Button
          variant="outlined"
          color="error"
          startIcon={<StopIcon />}
          onClick={handleStopSession}
          sx={{ 
            color: 'secondary.contrastText', 
            borderColor: 'secondary.contrastText',
            '&:hover': {
              bgcolor: 'error.main',
              borderColor: 'error.main',
              color: 'error.contrastText'
            }
          }}
        >
          End Session
        </Button>
      </Box>

      <Divider sx={{ bgcolor: 'secondary.contrastText', opacity: 0.3, mb: 2 }} />

      {/* Timer instances */}
      <Stack direction="row" spacing={2} sx={{ alignItems: 'center' }}>
        {(activeSession.timerInstances || []).map((instance, index) => (
          <TimerInstanceCard
            key={instance.id}
            instance={instance}
            index={index + 1}
            isRunning={instance.isRunning}
            elapsed={instance.isRunning ? elapsed : (instance.duration || 0)}
            onStart={() => onStartTimer(instance.id)}
            onStop={() => onStopTimer(instance.id)}
            onPause={() => onPauseTimer(instance.id)}
            onResume={() => onResumeTimer(instance.id)}
            onOpenNotes={() => onOpenTimerNotes?.(instance.id)}
          />
        ))}
        
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          onClick={handleAddTimerInstance}
          sx={{ 
            color: 'secondary.contrastText', 
            borderColor: 'secondary.contrastText',
            minWidth: 140
          }}
        >
          Add Timer
        </Button>
      </Stack>

      {/* Session menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          onOpenSessionNotes?.(activeSession.id);
          handleMenuClose();
        }}>
          <ListItemIcon>
            <NotesIcon />
          </ListItemIcon>
          <ListItemText>Session Notes</ListItemText>
        </MenuItem>
      </Menu>
    </Paper>
  );
}

// Timer Instance Card Component
interface TimerInstanceCardProps {
  instance: TimerInstance;
  index: number;
  isRunning: boolean;
  elapsed: number;
  onStart: () => void;
  onStop: () => void;
  onPause: () => void;
  onResume: () => void;
  onOpenNotes: () => void;
}

function TimerInstanceCard({
  index,
  isRunning,
  elapsed,
  onStart,
  onStop,
  onPause,
  onOpenNotes,
}: TimerInstanceCardProps) {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: 1,
        p: 1.5,
        borderRadius: 1,
        bgcolor: isRunning ? 'success.dark' : 'secondary.dark',
        color: 'white',
        minWidth: 120,
      }}
    >
      <Typography variant="caption" sx={{ fontWeight: 600, opacity: 0.8 }}>
        Timer {index}
      </Typography>
      
      <Typography
        variant="body1"
        sx={{
          fontFamily: 'monospace',
          fontWeight: 600,
          fontSize: '0.9rem'
        }}
      >
        {formatTime(elapsed)}
      </Typography>
      
      <Box sx={{ display: 'flex', gap: 0.5 }}>
        {!isRunning ? (
          <IconButton size="small" onClick={onStart} sx={{ color: 'white' }}>
            <PlayIcon fontSize="small" />
          </IconButton>
        ) : (
          <>
            <IconButton size="small" onClick={onPause} sx={{ color: 'white' }}>
              <PauseIcon fontSize="small" />
            </IconButton>
            <IconButton size="small" onClick={onStop} sx={{ color: 'white' }}>
              <StopIcon fontSize="small" />
            </IconButton>
          </>
        )}
        
        <IconButton size="small" onClick={onOpenNotes} sx={{ color: 'white' }}>
          <NoteAddIcon fontSize="small" />
        </IconButton>
      </Box>
    </Box>
  );
}
